<template>
  <div
    class="editable-textarea-wrapper"
    @mouseenter="showIcon = true"
    @mouseleave="showIcon = false">
    <el-input
      :ref="refName"
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 6 }"
      resize="none"
      v-model="internalValue"
      :readonly="readonly || !editing"
      :class="{
        'modified-field': isModified,
        'editing-field': editing,
      }"
      :placeholder="placeholder"
      style="width: 100%"
      @dblclick.native="handleDoubleClick"
      @blur="handleBlur"
      @keydown.native="handleKeydown">
    </el-input>
    <theme-icon
      v-show="showIcon"
      name="edit-law"
      class="hover-icon"></theme-icon>
  </div>
</template>

<script>
export default {
  name: 'EditableTextarea',
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    isModified: {
      type: Boolean,
      default: false,
    },
    refName: {
      type: String,
      default: 'editableTextarea',
    },
  },
  data() {
    return {
      editing: false,
      originalEditValue: '',
      showIcon: false,
    };
  },
  computed: {
    internalValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      },
    },
  },
  methods: {
    handleDoubleClick(event) {
      event.preventDefault();
      if (this.readonly) {
        return;
      }
      this.originalEditValue = this.value;
      this.editing = true;
      this.$nextTick(() => {
        if (this.$refs[this.refName]) {
          const textareaEl =
            this.$refs[this.refName].$el.querySelector('textarea');
          if (textareaEl) {
            textareaEl.focus();
            textareaEl.setSelectionRange(
              textareaEl.value.length,
              textareaEl.value.length,
            );
          }
        }
      });
    },

    handleBlur() {
      if (this.editing) {
        this.editing = false;
        this.$emit('blur');
      }
    },

    handleKeydown(event) {
      if (!this.editing) {
        return;
      }

      if (event.key === 'Escape') {
        event.preventDefault();
        this.internalValue = this.originalEditValue;
        this.editing = false;
        this.$refs[this.refName].blur();
      } else if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        this.editing = false;
        this.$refs[this.refName].blur();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.editable-textarea-wrapper {
  position: relative;
  width: 100%;

  .hover-icon {
    position: absolute;
    bottom: 8px;
    right: 8px;
    cursor: pointer;
    z-index: 10;
  }
}

.modified-field {
  color: red;
  ::v-deep .el-input__inner,
  ::v-deep .el-textarea__inner {
    border-color: #f56c6c !important;
  }
}

.editing-field {
  ::v-deep .el-textarea__inner {
    border-color: #409eff !important;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
  }
}
</style>
