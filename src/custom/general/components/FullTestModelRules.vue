<template>
  <div v-loading="isLoading">
    <div class="container">
      <div class="header">
        <el-button @click="handleGoBack" type="primary" size="medium"
          >返回</el-button
        >
        <span>选择文档</span>
        <el-select
          size="small"
          v-model="chatdoc_id"
          placeholder="请选择测试文档"
          style="width: 300px">
          <el-option
            v-for="item in documentList"
            :key="item.id"
            :value="item.chatdoc_id"
            :label="item.name"></el-option>
        </el-select>
        <span>选择法规</span>
        <el-select
          multiple
          collapse-tags
          size="small"
          v-model="orderIds"
          placeholder="请选择法规"
          class="law-select"
          style="width: 300px"
          @change="handleLawChange">
          <el-option
            v-for="item in lawsOptions"
            :key="item.id"
            :value="item.id"
            :label="item.name"></el-option>
        </el-select>
        <el-button type="primary" size="medium">开始测试</el-button>
        <el-button type="primary" size="medium">提交审核</el-button>
      </div>

      <div class="search-form">
        <el-select
          v-model="localSearchForm.field"
          size="medium"
          class="search-field">
          <el-option
            v-for="(item, index) in searchOptions"
            :key="index"
            :label="item.label"
            :value="item.value"></el-option>
        </el-select>
        <el-input
          v-model.trim="localSearchForm.keyword"
          :placeholder="currentSearchOption.placeholder"
          size="medium"
          clearable
          class="search-input"
          @clear="handleSearchClick"
          @keydown.enter.native="handleSearchClick">
        </el-input>
        <el-button
          type="primary"
          size="medium"
          class="search-btn"
          @click="handleSearchClick">
          查询
        </el-button>
      </div>
    </div>
    <el-table
      :data="tableData"
      border
      height="calc(100vh - 138px)"
      ref="table"
      class="rule-list-table has-border"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column
        width="100"
        prop="id"
        label="ID"
        header-align="center"></el-table-column>
      <el-table-column
        min-width="100"
        prop="order.name"
        label="法规名称"
        header-align="center"></el-table-column>
      <el-table-column
        min-width="100"
        prop="nowData.name"
        label="规则名称"
        align="center"></el-table-column>
      <el-table-column
        min-width="300"
        prop="nowData.rule_content"
        label="法规原文"
        header-align="center"></el-table-column>
      <el-table-column
        min-width="300"
        prop="nowData.core"
        label="核心要求"
        header-align="center"></el-table-column>
      <el-table-column
        min-width="400"
        prop="nowData.check_method"
        header-align="center"
        label="验证方式"></el-table-column>
      <el-table-column width="100" prop="id" align="center">
        <template slot="header" slot-scope="{}">
          <el-popover
            placement="bottom"
            width="160"
            popper-class="model-rule-column-select-popper"
            trigger="click">
            <div
              @click="handleStatusAllClick"
              class="option-item"
              :class="{
                'is-active': filterParams.status.length === columnStatusNums,
              }">
              <span>全部</span>
              <i class="el-icon-check"></i>
            </div>
            <div
              v-for="(value, key) in ROW_STATUS_MAP"
              :key="value"
              class="option-item"
              :class="{
                'is-active': filterParams.status.includes(key),
              }"
              @click="handleLawStatusItemClick(key)">
              <span>{{ value }}</span>
              <i class="el-icon-check"></i>
            </div>
            <span slot="reference" class="option-header">
              是否合规<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <div>{{ ROW_STATUS_MAP[scope.row.nowData.compliance] }}</div>
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" header-align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)"
            >详情</el-button
          >
          <el-button type="text" @click="handleRelate(scope.row)"
            >关联法规</el-button
          >
          <el-button type="text" @click="handleReTest(scope.row)"
            >重新测试</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <related-laws-dialog v-if="showRelatedLawsDialog" />
  </div>
</template>
<script>
import RelatedLawsDialog from './RelatedLawsDialog.vue';
import { laws as lawsApi } from '@/store/api';
import _ from 'lodash';

const ROW_STATUS = {
  WAITING: 'WAITING',
  NOT_COMPLIANCE: 'NOT_COMPLIANCE',
  NOT_INVOLVED: 'NOT_INVOLVED',
  COMPLIANCE: 'COMPLIANCE',
  UNCERTAIN: 'UNCERTAIN',
};
export default {
  name: 'full-test-model-rules',
  components: {
    RelatedLawsDialog,
  },
  data() {
    return {
      chatdoc_id: '',
      orderIds: [],
      tableData: [],
      formateTableOriginData: [],
      ROW_STATUS_MAP: {
        [ROW_STATUS.COMPLIANCE]: '合规',
        [ROW_STATUS.NOT_COMPLIANCE]: '不合规',
        [ROW_STATUS.NOT_INVOLVED]: '不涉及',
        [ROW_STATUS.WAITING]: '分析中',
        [ROW_STATUS.UNCERTAIN]: '-',
      },
      filterParams: {
        status: [],
      },
      showRelatedLawsDialog: false,
      localSearchForm: {
        field: 'law_name',
        keyword: '',
      },
      searchOptions: [
        { label: '法规名称', value: 'law_name', placeholder: '请输入法规名称' },
        {
          label: '法规内容',
          value: 'rule_content',
          placeholder: '请输入法规内容',
        },
        { label: '规则名称', value: 'name', placeholder: '请输入规则名称' },
      ],
      isLoading: false,
      lawsOptions: [],
      documentList: [],
    };
  },
  computed: {
    columnStatusNums() {
      return Object.keys(this.ROW_STATUS_MAP).length;
    },
    currentSearchOption() {
      return this.searchOptions.find(
        (option) => option.value === this.localSearchForm.field,
      );
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getCheckPoints();
      this.getLawsOrders();
      this.getDocumentList();
    },
    handleLawChange() {
      // reset data
      this.getCheckPoints();
    },
    handleSelectionChange(rows) {
      console.log(rows);
    },
    async getDocumentList() {
      try {
        const { files } = await lawsApi.getTestFileList();
        this.documentList = files;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
    async getLawsOrders() {
      try {
        const data = await lawsApi.getLawsOrders();
        this.lawsOptions = data;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },

    async getCheckPoints(options = { loading: true }) {
      try {
        if (this.orderIds.length === 0) {
          return;
        }
        if (options.loading) {
          this.isLoading = true;
        }
        const { items } = await lawsApi.getCheckPoints({
          order_ids: this.orderIds,
          size: 1000,
          page: 1,
        });
        const formateTableAllData = this.formatTableData(items);
        this.formateTableOriginData = _.cloneDeep(formateTableAllData);
        this.tableData = this.filterTableData(formateTableAllData);
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        if (options.loading) {
          this.isLoading = false;
        }
      }
    },
    filterTableData() {
      if (this.localSearchForm.keyword) {
        return this.formateTableOriginData.filter((item) =>
          item.nowData[this.localSearchForm.field].includes(
            this.localSearchForm.keyword,
          ),
        );
      }
      return this.formateTableOriginData;
    },
    formatTableData(data) {
      return data.map((item) => {
        const nowData = item.draft ? item.draft : item;
        const {
          check_method,
          check_type,
          core,
          id,
          name,
          review_status,
          rule_content,
          subject,
          updated_by_id,
          reviewer_id,
          meta,
        } = nowData;
        const order = item.order;
        return {
          ...item,
          nowData: {
            check_method,
            check_type,
            core,
            id,
            name,
            review_status,
            rule_content,
            subject,
            updated_by_id,
            reviewer_id,
            meta,
            law_name: order.name,
            compliance: ROW_STATUS.UNCERTAIN,
          },
        };
      });
    },
    handleSearchClick() {
      this.tableData = this.filterTableData(this.tableData);
    },
    handleStatusAllClick() {
      if (this.filterParams.status.length < this.lwaStatusNums) {
        this.filterParams.status = Object.keys(this.LAW_STATUS_MAP);
      } else {
        this.filterParams.status = [];
      }
      this.handleSearchClick();
    },
    handleLawStatusItemClick(val) {
      const isExist = this.filterParams.status.includes(val);
      if (isExist) {
        this.filterParams.status = this.filterParams.status.filter((item) => {
          return item !== val;
        });
      } else {
        this.filterParams.status.push(val);
      }
      this.handleSearchClick();
    },
    handleLawStatusAllClick() {
      if (this.filterParams.status.length < this.columnStatusNums) {
        this.filterParams.status = Object.keys(this.ROW_STATUS_MAP);
      } else {
        this.filterParams.status = [];
      }
      this.handleSearchClick();
    },
    handleGoBack() {
      this.$emit('goBack');
    },
    handleDetail(row) {
      this.$emit('detail', row);
    },
    handleRelate(row) {
      console.log(row);
      this.showRelatedLawsDialog = true;
    },
    handleReTest(row) {
      this.$emit('reTest', row);
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .search-form {
    display: flex;
    align-items: center;
    .search-field {
      width: 110px;
    }
    .search-input {
      width: 200px;
    }
  }
}
.header {
  display: flex;
  margin: 20px;
  column-gap: 20px;
  align-items: center;
}
.law-select {
  ::v-deep .el-tag {
    max-width: 210px;
  }
}
</style>
<style lang="scss">
.el-popover.model-rule-column-select-popper {
  padding: 10px 0;
  max-height: 388px;
  overflow-y: auto;
  .option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    padding: 0 20px;
    cursor: pointer;

    &:hover {
      background: #f3f7fc;
      color: $--color-primary;
    }

    .el-icon-check {
      visibility: hidden;
      color: $--color-primary;
    }

    &.is-active {
      background: #f3f7fc;
      color: $--color-primary;
      .el-icon-check {
        visibility: visible;
      }
    }
  }
}
</style>
