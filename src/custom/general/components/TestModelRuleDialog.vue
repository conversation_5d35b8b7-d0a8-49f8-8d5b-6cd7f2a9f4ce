<template>
  <el-dialog
    title="测试"
    width="600px"
    :visible="true"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    @close="closeDialog">
    <div>
      <el-form
        ref="form"
        class="test-rule-form"
        :model="form"
        label-width="80px"
        label-position="left">
        <el-form-item label="选择文档">
          <div class="document-select">
            <el-select
              v-model="form.chatdoc_id"
              clearable
              size="small"
              placeholder="请选择一份解析完成的文档"
              style="width: 100%">
              <el-option
                v-for="item in documentList"
                :key="item.id"
                :value="item.chatdoc_id"
                :label="item.name"></el-option>
            </el-select>
            <theme-icon
              class="test-icon"
              name="test-law"
              icon-class="el-icon-edit"
              @click.native.stop="handleTest"></theme-icon>
          </div>
        </el-form-item>
      </el-form>
      <div class="test-result-container">
        <div class="test-result">测试结果</div>
        <template v-if="testResult || testLoading">
          <div class="result-list">
            <div class="result-item">
              <div class="result-label">法规原文</div>
              <div class="result-content">
                {{ testLoading ? '系统处理中...' : testRuleInfo.rule_content }}
              </div>
            </div>
            <div class="result-item">
              <div class="result-label">合同片段</div>
              <div class="result-content">
                <span v-if="testLoading">系统处理中...</span>
                <span
                  v-else
                  v-text="testResult.contract_rects"
                  style="white-space: pre-line"></span>
              </div>
            </div>
            <div class="result-item">
              <div class="result-label">是否合规</div>
              <div class="result-content">
                {{
                  testLoading ? '系统处理中...' : testResult.compliance_status
                }}
              </div>
            </div>
            <div class="result-item">
              <div class="result-label">判定依据</div>
              <div class="result-content">
                {{ testLoading ? '系统处理中...' : testResult.judgment_basis }}
              </div>
            </div>
            <div class="result-item">
              <div class="result-label">修改意见</div>
              <div class="result-content">
                {{ testLoading ? '系统处理中...' : testResult.suggestion }}
              </div>
            </div>
          </div>
          <div></div>
        </template>
        <div class="empty" v-else>
          <p>请在上方选择文档</p>
          <p>文档选择完成后，请点击开始测试</p>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog" size="small">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { laws as lawsApi } from '@/store/api';
export default {
  name: 'test-rule-dialog',
  props: {
    documentList: {
      type: Array,
      default: () => [],
    },
    id: {
      type: Number,
      required: true,
    },
    testRuleInfo: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      form: {
        document_ids: [],
      },
      testResult: null,
      testLoading: false,
      abortController: null,
    };
  },
  methods: {
    async handleTest() {
      if (!this.form.chatdoc_id) {
        this.$notify({
          title: '请选择测试文档',
          type: 'warning',
        });
      }
      if (this.abortController) {
        this.abortController.abort();
      }

      this.testLoading = true;
      this.abortController = new AbortController();

      try {
        const { rule_content, name, subject, check_type, core, check_method } =
          this.testRuleInfo;

        const data = await lawsApi.ContractAnalysisByCheckPoint(
          this.id,
          {
            rule_content,
            name,
            subject,
            check_type,
            core,
            check_method,
            chatdoc_id: this.form.chatdoc_id,
          },
          { signal: this.abortController.signal },
        );

        if (!data) {
          return;
        }
        data.contract_rects = data.contract_rects
          .map((item) => item[0])
          .join('\n');
        this.testResult = data;
      } catch (error) {
        if (error && error.message) {
          this.$notify({
            title: '错误',
            message: error?.message || '请求失败',
            type: 'error',
          });
        }
      } finally {
        this.testLoading = false;
        this.abortController = null;
      }
    },
    closeDialog() {
      this.$emit('close');
    },
  },
};
</script>
<style lang="scss" scoped>
.document-select {
  display: flex;
}
.tips {
  line-height: 20px;
  font-size: 12px;
  color: #b14435;
}
.test-rule-form {
  padding: 0 40px;
}
.test-result-container {
  position: relative;
  border: 1px solid #e9e5e5;
  border-radius: 6px;
  padding: 20px;
  min-height: 160px;

  .empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #ccc;
    text-align: center;
  }

  .result-list {
    margin-top: 10px;
  }

  .result-item {
    display: flex;
    margin-bottom: 20px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .result-label {
    width: 80px;
    flex-shrink: 0;
    font-weight: 500;
    color: #333;
    font-size: 14px;
    line-height: 1.5;
    margin-right: 20px;
  }

  .result-content {
    flex: 1;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    word-break: break-all;
    border-radius: 4px;
    border: 1px solid #e9e5e5;
    padding: 6px;
    min-height: 21px;
    max-height: 80px;
    overflow-y: auto;
  }
}
.test-result {
  font-size: 16px;
  font-weight: bold;
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.test-icon {
  cursor: pointer;
  width: 32px;
  margin-left: 10px;
}
</style>
